# Norman's Quantum-Proof Encryption Program
## Troubleshooting Guide

---

## 🚨 **Common Issues & Solutions**

### **🔧 Installation Issues**

#### **Issue: Rust Compilation Errors**
```
error: failed to compile `normans-quantum-proof-encryption`
```
**Solution:**
```bash
# Update Rust to latest version
rustup update

# Clean and rebuild
cargo clean
cargo build --release --features web-server

# Check Rust version (requires 1.70+)
rustc --version
```

#### **Issue: Missing Dependencies**
```
error: could not find `openssl` in the registry
```
**Solution:**
```bash
# Ubuntu/Debian
sudo apt-get install pkg-config libssl-dev

# CentOS/RHEL
sudo yum install openssl-devel

# macOS
brew install openssl
```

---

### **🌐 Web Server Issues**

#### **Issue: Port Already in Use**
```
Error: Address already in use (os error 98)
```
**Solution:**
```bash
# Check what's using the port
sudo netstat -tulpn | grep :8080

# Kill the process
sudo kill -9 <PID>

# Or use a different port
./target/release/normans-quantum-proof-encryption --web --port 8081
```

#### **Issue: Cannot Access Web Interface**
```
Browser shows "This site can't be reached"
```
**Solution:**
```bash
# Check if server is running
ps aux | grep normans-quantum

# Check firewall settings
sudo ufw status
sudo ufw allow 8080

# Try localhost specifically
curl http://localhost:8080
```

#### **Issue: Web Interface Loads but Doesn't Work**
```
JavaScript errors in browser console
```
**Solution:**
1. **Clear browser cache** (Ctrl+F5)
2. **Check browser console** for specific errors
3. **Try different browser** (Chrome, Firefox, Safari)
4. **Disable browser extensions** that might interfere

---

### **🔒 Encryption/Decryption Issues**

#### **Issue: "Invalid tag or corrupted data"**
```
Decryption failed: Invalid tag or corrupted data
```
**Causes & Solutions:**
1. **Wrong Password**: Verify password is exactly the same
2. **Corrupted Data**: Check if encrypted text was copied completely
3. **Wrong Key**: Ensure you're using the correct private key
4. **Encoding Issues**: Avoid copying from formatted text (Word, etc.)

#### **Issue: "Ghost format parsing error"**
```
Decryption error: Invalid ghost format
```
**Solution:**
```bash
# Check format: should be FORMAT:ALGORITHM:NOISE:DATA:TRAILER
echo "ENCRYPTED_BLOB:Serpent:noise:data:12345" | wc -c

# Ensure no line breaks or extra characters
cat encrypted.txt | tr -d '\n\r' > clean_encrypted.txt
```

#### **Issue: File Upload Fails**
```
Upload failed: File too large
```
**Solution:**
```bash
# Check file size (max 25GB)
ls -lh yourfile.dat

# For very large files, use command line instead
./target/release/normans-quantum-proof-encryption encrypt \
  --input largefile.dat \
  --output largefile.dat.qpe \
  --password "your-password"
```

---

### **💾 Memory & Performance Issues**

#### **Issue: Out of Memory Errors**
```
thread 'main' panicked at 'out of memory'
```
**Solution:**
```bash
# Check available memory
free -h

# Increase swap space
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# Use command line for very large files
./target/release/normans-quantum-proof-encryption encrypt \
  --input hugefile.dat \
  --output hugefile.dat.qpe \
  --password "password"
```

#### **Issue: Slow Performance**
```
Encryption taking very long time
```
**Solutions:**
1. **Lower Security Level**: Use "standard" instead of "maximum"
2. **Reduce Compression**: Use low compression for faster processing
3. **Check System Resources**: Monitor CPU and memory usage
4. **Use SSD Storage**: Faster disk I/O improves performance

---

### **🔑 Key Management Issues**

#### **Issue: "Private key password required"**
```
Error: Private key is encrypted but no password provided
```
**Solution:**
```bash
# Provide key password when decrypting
./target/release/normans-quantum-proof-encryption decrypt \
  --input file.qpe \
  --output file.dat \
  --private-key alice.key \
  --password "key-password"
```

#### **Issue: "Invalid private key format"**
```
Error: Failed to load private key
```
**Solution:**
1. **Check Key File**: Ensure it's a valid .key file
2. **Verify Password**: Try the correct key password
3. **Check File Corruption**: Re-generate keys if corrupted
4. **File Permissions**: Ensure you can read the key file

---

### **📁 File & Folder Issues**

#### **Issue: Download Fails with "Not Found"**
```
Download failed: Download failed: Not Found
```
**Solution:**
```bash
# Check downloads directory exists
ls -la downloads/

# Restart server to recreate directory
./target/release/normans-quantum-proof-encryption --web --port 8080
```

#### **Issue: Folder Encryption Creates Empty Archive**
```
Downloaded file is empty or corrupted
```
**Solution:**
1. **Check Source Folder**: Ensure it contains files
2. **Verify Permissions**: Check read permissions on all files
3. **Check Disk Space**: Ensure enough space for compressed archive
4. **Try Individual Files**: Encrypt files one by one if folder fails

---

### **🎯 Browser-Specific Issues**

#### **Chrome Issues**
```
ERR_CONTENT_LENGTH_MISMATCH
```
**Solution:**
```bash
# Clear Chrome cache
chrome://settings/clearBrowserData

# Disable Chrome extensions
chrome://extensions/

# Try incognito mode
Ctrl+Shift+N
```

#### **Firefox Issues**
```
Network protocol error
```
**Solution:**
```bash
# Clear Firefox cache
about:preferences#privacy

# Disable tracking protection for localhost
about:config -> privacy.trackingprotection.enabled = false
```

#### **Safari Issues**
```
Cannot download file
```
**Solution:**
1. **Enable Downloads**: Safari > Preferences > General
2. **Allow Pop-ups**: For download dialogs
3. **Check Security Settings**: Allow downloads from localhost

---

### **🔍 Debugging Steps**

#### **Enable Debug Logging**
```bash
# Full debug output
RUST_LOG=debug ./target/release/normans-quantum-proof-encryption --web

# Specific module debugging
RUST_LOG=quantum_encrypt::encryption=debug ./target/release/normans-quantum-proof-encryption --web
```

#### **Test API Directly**
```bash
# Test text encryption
curl -X POST http://localhost:8080/api/encrypt/text \
  -H "Content-Type: application/json" \
  -d '{"operation_type": "text", "input": "test", "method": "password", "password": "test123"}'

# Test server health
curl http://localhost:8080/api/health
```

#### **Check System Resources**
```bash
# Monitor memory usage
watch -n 1 'free -h'

# Monitor CPU usage
top -p $(pgrep normans-quantum)

# Check disk space
df -h
```

---

### **🆘 Emergency Recovery**

#### **Corrupted Encrypted File**
1. **Check File Integrity**: Verify file size and format
2. **Try Different Passwords**: In case of typos
3. **Use Backup**: If available
4. **Contact Support**: For advanced recovery options

#### **Lost Private Key**
1. **Check Backups**: Look for key backups
2. **Search System**: Find .key files
3. **Recovery Impossible**: Without private key, data cannot be recovered
4. **Prevention**: Always backup private keys securely

#### **System Crash During Encryption**
1. **Check Temporary Files**: May contain partial data
2. **Restart Process**: Re-encrypt from original file
3. **Check Disk Space**: Ensure sufficient space
4. **Use Command Line**: More reliable for large files

---

### **📞 Getting Help**

#### **Log Collection**
```bash
# Collect system info
uname -a > system_info.txt
free -h >> system_info.txt
df -h >> system_info.txt

# Collect application logs
RUST_LOG=debug ./target/release/normans-quantum-proof-encryption --web 2>&1 | tee app_log.txt
```

#### **Bug Reports**
Include the following information:
1. **Operating System**: Version and architecture
2. **Rust Version**: `rustc --version`
3. **Application Version**: `--version` output
4. **Error Messages**: Complete error text
5. **Steps to Reproduce**: Detailed reproduction steps
6. **Log Files**: Debug logs if available

#### **Support Channels**
- **GitHub Issues**: For bug reports and feature requests
- **Documentation**: Check operation manual and README
- **Community**: User forums and discussions
- **Enterprise Support**: Commercial support available

---

## ✅ **Prevention Tips**

1. **Regular Backups**: Backup keys and important encrypted files
2. **Test Procedures**: Regularly test encryption/decryption workflows
3. **Monitor Resources**: Keep an eye on system performance
4. **Update Software**: Keep Rust and dependencies updated
5. **Secure Environment**: Use HTTPS and secure networks in production

---

This troubleshooting guide covers the most common issues. For additional help, consult the operation manual or contact support.
