use crate::errors::{EncryptionError, Result};
use crate::key_management::KeyManager;
use crate::models::*;
use crate::streaming::{encrypt_file as stream_encrypt_file, decrypt_file as stream_decrypt_file};
use crate::utils::{decode_base64, encode_base64, get_file_permissions};
use chrono::{DateTime, Utc};
use indicatif::{ProgressBar, ProgressStyle};
use std::fs::{self, File};
use std::io::{self, BufReader, BufWriter};
use std::path::{Path, PathBuf};
use tempfile::NamedTempFile;
use filetime::{FileTime, set_file_mtime};
use zip::write::FileOptions;
use zip::{CompressionMethod, ZipArchive, ZipWriter};

pub struct QuantumProofEncryption {
    security_level: SecurityLevel,
    key_manager: KeyManager,
}

impl QuantumProofEncryption {
    /// Create a new instance with the specified security level
    pub fn new(security_level: SecurityLevel) -> Self {
        let key_manager = KeyManager::new(security_level);
        Self {
            security_level,
            key_manager,
        }
    }

    /// Generate a new quantum-safe keypair
    pub fn generate_keypair(&self) -> Result<(Vec<u8>, Vec<u8>)> {
        self.key_manager.generate_keypair()
    }

    /// Save keypair to files
    pub fn save_keypair(
        &self,
        public_key: &[u8],
        private_key: &[u8],
        pub_path: &Path,
        priv_path: &Path,
        key_password: Option<&str>,
    ) -> Result<()> {
        self.key_manager.save_keypair(public_key, private_key, pub_path, priv_path, key_password)
    }

    /// Load a key from file
    pub fn load_key(&self, key_path: &Path, key_password: Option<&str>) -> Result<(Vec<u8>, KeyMetadata)> {
        self.key_manager.load_key(key_path, key_password)
    }

    /// Encrypt a file with progress indication
    pub fn encrypt_file(
        &self,
        input_path: &Path,
        output_path: &Path,
        public_key: Option<&[u8]>,
        password: Option<&str>,
        show_progress: bool,
    ) -> Result<()> {
        if !input_path.exists() {
            return Err(EncryptionError::FileNotFound(
                input_path.to_string_lossy().to_string(),
            ));
        }

        let file_size = fs::metadata(input_path)?.len();
        
        if show_progress {
            let pb = ProgressBar::new(file_size);
            pb.set_style(
                ProgressStyle::default_bar()
                    .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {bytes}/{total_bytes} ({eta})")
                    .unwrap()
                    .progress_chars("#>-"),
            );

            // Use a custom streaming encryptor with progress callback
            let pb_clone = pb.clone();
            let encryptor = crate::streaming::StreamingEncryptor::new(self.security_level)
                .with_progress_callback(move |processed, _total| {
                    pb_clone.set_position(processed);
                });

            let metadata = fs::metadata(input_path)?;
            let file_metadata = FileMetadata {
                original_name: input_path.file_name()
                    .ok_or_else(|| EncryptionError::FileNotFound("Invalid file name".to_string()))?
                    .to_string_lossy()
                    .to_string(),
                original_size: metadata.len(),
                modified: DateTime::<Utc>::from(metadata.modified()?),
                permissions: get_file_permissions(input_path)?,
            };

            let input_file = File::open(input_path)?;
            let output_file = File::create(output_path)?;
            let mut reader = BufReader::new(input_file);
            let mut writer = BufWriter::new(output_file);

            if let Some(pub_key) = public_key {
                encryptor.encrypt_stream_public_key(&mut reader, &mut writer, file_metadata, pub_key)?;
            } else if let Some(pwd) = password {
                encryptor.encrypt_stream_password(&mut reader, &mut writer, file_metadata, pwd)?;
            } else {
                return Err(EncryptionError::Key("Either public key or password must be provided".to_string()));
            }

            pb.finish_with_message("Encryption complete");
        } else {
            stream_encrypt_file(input_path, output_path, self.security_level, public_key, password)?;
        }

        Ok(())
    }

    /// Decrypt a file with progress indication
    pub fn decrypt_file(
        &self,
        input_path: &Path,
        output_path: &Path,
        private_key: Option<&[u8]>,
        password: Option<&str>,
        show_progress: bool,
    ) -> Result<()> {
        if !input_path.exists() {
            return Err(EncryptionError::FileNotFound(
                input_path.to_string_lossy().to_string(),
            ));
        }

        if show_progress {
            let file_size = fs::metadata(input_path)?.len();
            let pb = ProgressBar::new(file_size);
            pb.set_style(
                ProgressStyle::default_bar()
                    .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {bytes}/{total_bytes} ({eta})")
                    .unwrap()
                    .progress_chars("#>-"),
            );

            // Use custom streaming decryptor with progress
            let pb_clone = pb.clone();
            let decryptor = crate::streaming::StreamingEncryptor::new(self.security_level)
                .with_progress_callback(move |processed, _total| {
                    pb_clone.set_position(processed);
                });

            let input_file = File::open(input_path)?;
            let output_file = File::create(output_path)?;
            let mut reader = BufReader::new(input_file);
            let mut writer = BufWriter::new(output_file);

            let header = if let Some(priv_key) = private_key {
                decryptor.decrypt_stream_private_key(&mut reader, &mut writer, priv_key)?
            } else if let Some(pwd) = password {
                decryptor.decrypt_stream_password(&mut reader, &mut writer, pwd)?
            } else {
                return Err(EncryptionError::Key("Either private key or password must be provided".to_string()));
            };

            pb.finish_with_message("Decryption complete");

            // Restore metadata
            drop(writer);
            let mtime: i64 = header.file_metadata.modified.timestamp();
            let file_time = FileTime::from_unix_time(mtime, 0);
            let _ = set_file_mtime(output_path, file_time);

            #[cfg(unix)]
            if let Some(perms) = header.file_metadata.permissions {
                if let Ok(mode) = u32::from_str_radix(&perms, 8) {
                    crate::utils::set_file_permissions(output_path, mode)?;
                }
            }
        } else {
            stream_decrypt_file(input_path, output_path, self.security_level, private_key, password)?;
        }

        Ok(())
    }

    /// Encrypt a folder into an encrypted archive
    pub fn encrypt_folder(
        &self,
        folder_path: &Path,
        output_path: &Path,
        public_key: Option<&[u8]>,
        password: Option<&str>,
        compression_level: Option<i32>,
        show_progress: bool,
    ) -> Result<()> {
        if !folder_path.is_dir() {
            return Err(EncryptionError::FileNotFound(
                format!("'{}' is not a directory", folder_path.display()),
            ));
        }

        // Create temporary zip file
        let temp_zip = NamedTempFile::new()?;
        let temp_path = temp_zip.path().to_path_buf();

        {
            let file = File::create(&temp_path)?;
            let mut zip = ZipWriter::new(file);

            let options = FileOptions::default()
                .compression_method(CompressionMethod::Deflated);

            let options = if let Some(level) = compression_level {
                options.compression_level(Some(level))
            } else {
                options
            };

            // Collect all files
            let mut files = Vec::new();
            collect_files(folder_path, folder_path, &mut files)?;

            if show_progress {
                let pb = ProgressBar::new(files.len() as u64);
                pb.set_style(
                    ProgressStyle::default_bar()
                        .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} files")
                        .unwrap(),
                );

                for (relative_path, full_path) in &files {
                    zip.start_file(relative_path.to_string_lossy(), options)?;
                    let mut file = File::open(full_path)?;
                    io::copy(&mut file, &mut zip)?;
                    pb.inc(1);
                }

                pb.finish_with_message("Archive created");
            } else {
                for (relative_path, full_path) in &files {
                    zip.start_file(relative_path.to_string_lossy(), options)?;
                    let mut file = File::open(full_path)?;
                    io::copy(&mut file, &mut zip)?;
                }
            }

            zip.finish()?;
        }

        // Encrypt the zip file
        self.encrypt_file(&temp_path, output_path, public_key, password, show_progress)?;

        // Secure deletion of temp file is handled by NamedTempFile automatically
        Ok(())
    }

    /// Decrypt an encrypted folder archive
    pub fn decrypt_folder(
        &self,
        input_path: &Path,
        output_folder: &Path,
        private_key: Option<&[u8]>,
        password: Option<&str>,
        show_progress: bool,
    ) -> Result<()> {
        // Create output directory
        fs::create_dir_all(output_folder)?;

        // Create temporary file for decrypted zip
        let temp_zip = NamedTempFile::new()?;
        let temp_path = temp_zip.path().to_path_buf();

        // Decrypt to temporary file
        self.decrypt_file(input_path, &temp_path, private_key, password, show_progress)?;

        // Extract zip file
        let file = File::open(&temp_path)?;
        let mut archive = ZipArchive::new(file)?;

        if show_progress {
            let pb = ProgressBar::new(archive.len() as u64);
            pb.set_style(
                ProgressStyle::default_bar()
                    .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} files")
                    .unwrap(),
            );

            for i in 0..archive.len() {
                let mut file = archive.by_index(i)?;
                let outpath = output_folder.join(file.mangled_name());

                if file.is_dir() {
                    fs::create_dir_all(&outpath)?;
                } else {
                    if let Some(p) = outpath.parent() {
                        if !p.exists() {
                            fs::create_dir_all(p)?;
                        }
                    }
                    let mut outfile = File::create(&outpath)?;
                    io::copy(&mut file, &mut outfile)?;
                }

                // Set permissions on Unix
                #[cfg(unix)]
                {
                    use std::os::unix::fs::PermissionsExt;
                    if let Some(mode) = file.unix_mode() {
                        fs::set_permissions(&outpath, fs::Permissions::from_mode(mode))?;
                    }
                }

                pb.inc(1);
            }

            pb.finish_with_message("Extraction complete");
        } else {
            for i in 0..archive.len() {
                let mut file = archive.by_index(i)?;
                let outpath = output_folder.join(file.mangled_name());

                if file.is_dir() {
                    fs::create_dir_all(&outpath)?;
                } else {
                    if let Some(p) = outpath.parent() {
                        if !p.exists() {
                            fs::create_dir_all(p)?;
                        }
                    }
                    let mut outfile = File::create(&outpath)?;
                    io::copy(&mut file, &mut outfile)?;
                }

                #[cfg(unix)]
                {
                    use std::os::unix::fs::PermissionsExt;
                    if let Some(mode) = file.unix_mode() {
                        fs::set_permissions(&outpath, fs::Permissions::from_mode(mode))?;
                    }
                }
            }
        }

        Ok(())
    }

    /// Encrypt text to base64 (Ghost-Mode)
    pub fn encrypt_text(
        &self,
        text: &str,
        public_key: Option<&[u8]>,
        password: Option<&str>,
    ) -> Result<String> {
        if let Some(pwd) = password {
            self.encrypt_text_with_password_ghost(text, pwd)
        } else if let Some(pub_key) = public_key {
            self.encrypt_text_with_key_ghost(text, pub_key)
        } else {
            Err(EncryptionError::Key("Either public key or password required".to_string()))
        }
    }

    /// Decrypt base64 text (Ghost-Mode aware)
    pub fn decrypt_text(
        &self,
        encrypted_text: &str,
        private_key: Option<&[u8]>,
        password: Option<&str>,
    ) -> Result<String> {
        // Try advanced ghost-mode decryption first
        if let Ok(decrypted) = self.decrypt_text_ghost(encrypted_text, private_key, password) {
            return Ok(decrypted);
        }

        // Fall back to standard decryption for backward compatibility
        let encrypted_bytes = decode_base64(encrypted_text.trim())?;
        let mut input = io::Cursor::new(encrypted_bytes);
        let mut output = Vec::new();

        let decryptor = crate::streaming::StreamingEncryptor::new(self.security_level);

        if let Some(priv_key) = private_key {
            decryptor.decrypt_stream_private_key(&mut input, &mut output, priv_key)?;
        } else if let Some(pwd) = password {
            decryptor.decrypt_stream_password(&mut input, &mut output, pwd)?;
        } else {
            return Err(EncryptionError::Key("Either private key or password required".to_string()));
        }

        String::from_utf8(output)
            .map_err(|_| EncryptionError::Decryption("Invalid UTF-8 in decrypted text".to_string()))
    }

    /// Ghost-mode text encryption with password (obfuscated output)
    pub fn encrypt_text_with_password_ghost(&self, text: &str, password: &str) -> Result<String> {
        let plaintext = text.as_bytes();
        let mut output = Vec::new();

        // Create fake metadata to obfuscate real size
        let fake_size = self.generate_fake_size(plaintext.len());
        let metadata = FileMetadata {
            original_name: "data".to_string(), // Generic name
            original_size: fake_size,
            modified: Utc::now(),
            permissions: None,
        };

        let encryptor = crate::streaming::StreamingEncryptor::new(self.security_level);
        encryptor.encrypt_stream_password(plaintext, &mut output, metadata, password)?;

        // Apply ghost-mode obfuscation
        self.apply_ghost_obfuscation(&output)
    }

    /// Ghost-mode text encryption with public key (obfuscated output)
    pub fn encrypt_text_with_key_ghost(&self, text: &str, public_key: &[u8]) -> Result<String> {
        let plaintext = text.as_bytes();
        let mut output = Vec::new();

        // Create fake metadata to obfuscate real size
        let fake_size = self.generate_fake_size(plaintext.len());
        let metadata = FileMetadata {
            original_name: "data".to_string(), // Generic name
            original_size: fake_size,
            modified: Utc::now(),
            permissions: None,
        };

        let encryptor = crate::streaming::StreamingEncryptor::new(self.security_level);
        encryptor.encrypt_stream_public_key(plaintext, &mut output, metadata, public_key)?;

        // Apply ghost-mode obfuscation
        self.apply_ghost_obfuscation(&output)
    }

    /// Generate fake size to obfuscate real content length
    fn generate_fake_size(&self, real_size: usize) -> u64 {
        use rand::Rng;
        let mut rng = rand::thread_rng();

        // Generate a fake size that's 2-10x the real size
        let multiplier = rng.gen_range(2..=10);
        (real_size * multiplier) as u64
    }

    /// Apply advanced ghost-mode obfuscation to encrypted data
    fn apply_ghost_obfuscation(&self, encrypted_data: &[u8]) -> Result<String> {
        use rand::Rng;
        let mut rng = rand::thread_rng();

        // Convert to base64 first
        let base64_data = encode_base64(encrypted_data);

        // For now, use a simpler but more reliable approach
        // Just add random base64-looking padding and use a simple marker system
        let prefix_len = rng.gen_range(30..100);
        let suffix_len = rng.gen_range(30..100);

        let prefix: String = (0..prefix_len)
            .map(|_| {
                let chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
                chars.chars().nth(rng.gen_range(0..chars.len())).unwrap()
            })
            .collect();

        let suffix: String = (0..suffix_len)
            .map(|_| {
                let chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
                chars.chars().nth(rng.gen_range(0..chars.len())).unwrap()
            })
            .collect();

        // Simple marker: just append the data length and prefix length as hex
        let marker = format!("{:04x}{:04x}", base64_data.len(), prefix_len);

        Ok(format!("{}{}{}{}", prefix, base64_data, suffix, marker))
    }

    /// Obfuscate as pure base64 data with random padding
    fn obfuscate_as_base64_data(&self, data: &str, rng: &mut impl rand::Rng) -> Result<String> {
        // Add random prefix and suffix that look like base64
        let prefix_len = rng.gen_range(20..80);
        let suffix_len = rng.gen_range(20..80);

        let prefix: String = (0..prefix_len)
            .map(|_| {
                let chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
                chars.chars().nth(rng.gen_range(0..chars.len())).unwrap()
            })
            .collect();

        let suffix: String = (0..suffix_len)
            .map(|_| {
                let chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
                chars.chars().nth(rng.gen_range(0..chars.len())).unwrap()
            })
            .collect();

        // Embed the real data randomly within the noise
        let total_len = prefix.len() + data.len() + suffix.len();
        let start_pos = prefix.len();
        let end_pos = start_pos + data.len();

        // Create a marker system that doesn't look like metadata
        // Use a simple encoding: store the data length and prefix length
        let data_len_marker = format!("{:04x}", data.len().min(0xFFFF));
        let prefix_len_marker = format!("{:04x}", prefix_len.min(0xFFFF));

        // Hide markers in what looks like random hex at the end
        let fake_hash = format!("{}{}{:012x}", data_len_marker, prefix_len_marker, rng.gen::<u64>());

        Ok(format!("{}{}{}{}", prefix, data, suffix, fake_hash))
    }

    /// Obfuscate as hex data that looks like a hash or binary dump
    fn obfuscate_as_hex_data(&self, data: &str, rng: &mut impl rand::Rng) -> Result<String> {
        // Convert base64 to hex-like format
        let hex_data = data.chars()
            .map(|c| format!("{:02x}", c as u8))
            .collect::<String>();

        // Add random hex padding
        let prefix_len = rng.gen_range(40..120);
        let suffix_len = rng.gen_range(40..120);

        let prefix: String = (0..prefix_len)
            .map(|_| format!("{:02x}", rng.gen::<u8>()))
            .collect();

        let suffix: String = (0..suffix_len)
            .map(|_| format!("{:02x}", rng.gen::<u8>()))
            .collect();

        // Create position markers hidden in the hex
        let start_marker = format!("{:04x}", hex_data.len());
        let end_marker = format!("{:04x}", prefix.len());

        Ok(format!("{}{}{}{}{}", prefix, hex_data, suffix, start_marker, end_marker))
    }

    /// Obfuscate as URL-encoded data
    fn obfuscate_as_url_encoded(&self, data: &str, rng: &mut impl rand::Rng) -> Result<String> {
        // URL encode the data
        let encoded: String = data.chars()
            .map(|c| {
                if rng.gen_bool(0.3) { // Randomly encode some characters
                    format!("%{:02X}", c as u8)
                } else {
                    c.to_string()
                }
            })
            .collect();

        // Add fake URL parameters
        let fake_params = [
            "session", "token", "data", "payload", "content", "buffer",
            "stream", "chunk", "block", "segment"
        ];

        let param_count = rng.gen_range(2..6);
        let mut params = Vec::new();

        for _ in 0..param_count {
            let param_name = fake_params[rng.gen_range(0..fake_params.len())];
            let param_value: String = (0..rng.gen_range(10..30))
                .map(|_| {
                    let chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
                    chars.chars().nth(rng.gen_range(0..chars.len())).unwrap()
                })
                .collect();
            params.push(format!("{}={}", param_name, param_value));
        }

        // Hide the real data as one of the parameters
        params.insert(rng.gen_range(0..params.len()), format!("data={}", encoded));

        Ok(params.join("&"))
    }

    /// Obfuscate as mixed content that looks like various data formats
    fn obfuscate_as_mixed_content(&self, data: &str, rng: &mut impl rand::Rng) -> Result<String> {
        let format_type = rng.gen_range(0..3);

        match format_type {
            0 => {
                // JSON-like structure
                let fake_fields = ["id", "timestamp", "hash", "signature", "payload", "metadata"];
                let field = fake_fields[rng.gen_range(0..fake_fields.len())];
                Ok(format!(r#"{{"{}":"{}","version":"{}","checksum":"{}"}}"#,
                    field, data, rng.gen::<u32>(), rng.gen::<u64>()))
            },
            1 => {
                // XML-like structure
                let tags = ["data", "content", "payload", "stream", "buffer"];
                let tag = tags[rng.gen_range(0..tags.len())];
                Ok(format!("<{}>{}</{}><meta>{}</meta>", tag, data, tag, rng.gen::<u64>()))
            },
            _ => {
                // Base64 with fake headers
                let headers = ["Content-Type", "Authorization", "X-Token", "X-Data"];
                let header = headers[rng.gen_range(0..headers.len())];
                Ok(format!("{}:{}\r\n\r\n{}", header, rng.gen::<u64>(), data))
            }
        }
    }

    /// Decrypt advanced ghost-mode obfuscated text
    pub fn decrypt_text_ghost(
        &self,
        obfuscated_text: &str,
        private_key: Option<&[u8]>,
        password: Option<&str>,
    ) -> Result<String> {
        // Try different deobfuscation strategies

        // Strategy 1: Legacy 5-part colon format (backward compatibility)
        if obfuscated_text.contains(':') && obfuscated_text.split(':').count() == 5 {
            let parts: Vec<&str> = obfuscated_text.split(':').collect();
            let encrypted_base64 = parts[3];
            return self.decrypt_standard_format(encrypted_base64, private_key, password);
        }

        // Strategy 2: Simple base64 with hex markers (new simplified format)
        if let Ok(data) = self.extract_from_simple_format(obfuscated_text) {
            return self.decrypt_standard_format(&data, private_key, password);
        }

        // Strategy 3: Base64 with hex markers (complex format)
        if let Ok(data) = self.extract_from_base64_format(obfuscated_text) {
            return self.decrypt_standard_format(&data, private_key, password);
        }

        // Strategy 4: Hex format
        if let Ok(data) = self.extract_from_hex_format(obfuscated_text) {
            return self.decrypt_standard_format(&data, private_key, password);
        }

        // Strategy 5: URL-encoded format
        if let Ok(data) = self.extract_from_url_format(obfuscated_text) {
            return self.decrypt_standard_format(&data, private_key, password);
        }

        // Strategy 6: Mixed content formats (JSON, XML, headers)
        if let Ok(data) = self.extract_from_mixed_format(obfuscated_text) {
            return self.decrypt_standard_format(&data, private_key, password);
        }

        Err(EncryptionError::Decryption("Unable to extract encrypted data from obfuscated format".to_string()))
    }

    /// Decrypt using standard format (helper method)
    fn decrypt_standard_format(
        &self,
        encrypted_base64: &str,
        private_key: Option<&[u8]>,
        password: Option<&str>,
    ) -> Result<String> {
        let encrypted_bytes = decode_base64(encrypted_base64.trim())?;
        let mut input = io::Cursor::new(encrypted_bytes);
        let mut output = Vec::new();

        let decryptor = crate::streaming::StreamingEncryptor::new(self.security_level);

        if let Some(priv_key) = private_key {
            decryptor.decrypt_stream_private_key(&mut input, &mut output, priv_key)?;
        } else if let Some(pwd) = password {
            decryptor.decrypt_stream_password(&mut input, &mut output, pwd)?;
        } else {
            return Err(EncryptionError::Key("Either private key or password required".to_string()));
        }

        String::from_utf8(output)
            .map_err(|_| EncryptionError::Decryption("Invalid UTF-8 in decrypted text".to_string()))
    }

    /// Extract data from simple format (new simplified approach)
    fn extract_from_simple_format(&self, obfuscated: &str) -> Result<String> {
        // Look for 8 hex characters at the end (4 for data_len + 4 for prefix_len)
        if obfuscated.len() < 8 {
            return Err(EncryptionError::Decryption("Too short for simple format".to_string()));
        }

        let hex_part = &obfuscated[obfuscated.len()-8..];
        if hex_part.chars().all(|c| c.is_ascii_hexdigit()) {
            let data_len = u16::from_str_radix(&hex_part[0..4], 16).unwrap_or(0) as usize;
            let prefix_len = u16::from_str_radix(&hex_part[4..8], 16).unwrap_or(0) as usize;

            let content_part = &obfuscated[..obfuscated.len()-8];

            // Validate the extracted parameters
            if prefix_len < content_part.len() && data_len > 0 {
                let data_start = prefix_len;
                let data_end = data_start + data_len;

                if data_end <= content_part.len() {
                    return Ok(content_part[data_start..data_end].to_string());
                }
            }
        }

        Err(EncryptionError::Decryption("Invalid simple format".to_string()))
    }

    /// Extract data from base64 format with hex markers
    fn extract_from_base64_format(&self, obfuscated: &str) -> Result<String> {
        // Look for hex markers at the end (20 chars: 4 for data_len + 4 for prefix_len + 12 for random)
        if obfuscated.len() < 20 {
            return Err(EncryptionError::Decryption("Too short for base64 format".to_string()));
        }

        // Extract the hex markers from the end
        let hex_part = &obfuscated[obfuscated.len()-20..];
        if hex_part.chars().all(|c| c.is_ascii_hexdigit()) {
            let data_len = u16::from_str_radix(&hex_part[0..4], 16).unwrap_or(0) as usize;
            let prefix_len = u16::from_str_radix(&hex_part[4..8], 16).unwrap_or(0) as usize;

            let content_part = &obfuscated[..obfuscated.len()-20];

            // Validate the extracted parameters
            if prefix_len < content_part.len() && data_len > 0 {
                let data_start = prefix_len;
                let data_end = data_start + data_len;

                if data_end <= content_part.len() {
                    return Ok(content_part[data_start..data_end].to_string());
                }
            }
        }

        Err(EncryptionError::Decryption("Invalid base64 format".to_string()))
    }

    /// Extract data from hex format
    fn extract_from_hex_format(&self, obfuscated: &str) -> Result<String> {
        // Look for hex markers at the end
        if obfuscated.len() < 8 {
            return Err(EncryptionError::Decryption("Too short for hex format".to_string()));
        }

        let end_part = &obfuscated[obfuscated.len()-8..];
        if end_part.chars().all(|c| c.is_ascii_hexdigit()) {
            let data_len = u16::from_str_radix(&end_part[0..4], 16).unwrap_or(0) as usize;
            let prefix_len = u16::from_str_radix(&end_part[4..8], 16).unwrap_or(0) as usize;

            let hex_data_part = &obfuscated[..obfuscated.len()-8];
            if prefix_len < hex_data_part.len() && data_len > 0 {
                let hex_data = &hex_data_part[prefix_len..];
                if hex_data.len() >= data_len {
                    let hex_slice = &hex_data[..data_len];
                    // Convert hex back to base64-like string
                    let mut result = String::new();
                    for chunk in hex_slice.as_bytes().chunks(2) {
                        if chunk.len() == 2 {
                            if let Ok(byte_val) = u8::from_str_radix(
                                &String::from_utf8_lossy(chunk), 16
                            ) {
                                result.push(byte_val as char);
                            }
                        }
                    }
                    return Ok(result);
                }
            }
        }

        Err(EncryptionError::Decryption("Invalid hex format".to_string()))
    }

    /// Extract data from URL-encoded format
    fn extract_from_url_format(&self, obfuscated: &str) -> Result<String> {
        // Look for data= parameter
        for param in obfuscated.split('&') {
            if param.starts_with("data=") {
                let encoded_data = &param[5..]; // Skip "data="
                // URL decode
                let decoded = encoded_data.replace("%20", " ")
                    .replace("%3D", "=")
                    .replace("%2B", "+")
                    .replace("%2F", "/");

                // Simple URL decoding for common base64 characters
                let mut result = String::new();
                let mut chars = decoded.chars();
                while let Some(c) = chars.next() {
                    if c == '%' {
                        if let (Some(h1), Some(h2)) = (chars.next(), chars.next()) {
                            if let Ok(byte_val) = u8::from_str_radix(&format!("{}{}", h1, h2), 16) {
                                result.push(byte_val as char);
                            } else {
                                result.push(c);
                                result.push(h1);
                                result.push(h2);
                            }
                        } else {
                            result.push(c);
                        }
                    } else {
                        result.push(c);
                    }
                }

                return Ok(result);
            }
        }

        Err(EncryptionError::Decryption("No data parameter found in URL format".to_string()))
    }

    /// Extract data from mixed content formats (JSON, XML, headers)
    fn extract_from_mixed_format(&self, obfuscated: &str) -> Result<String> {
        // Try JSON format
        if obfuscated.starts_with('{') && obfuscated.ends_with('}') {
            // Look for common field patterns
            for field in ["id", "hash", "signature", "payload", "metadata"] {
                let pattern = format!(r#""{}":"#, field);
                if let Some(start_idx) = obfuscated.find(&pattern) {
                    let value_start = start_idx + pattern.len();
                    if let Some(end_idx) = obfuscated[value_start..].find('"') {
                        if value_start + end_idx < obfuscated.len() {
                            let value = &obfuscated[value_start..value_start + end_idx];
                            return Ok(value.to_string());
                        }
                    }
                }
            }
        }

        // Try XML format
        for tag in ["data", "content", "payload", "stream", "buffer"] {
            let open_tag = format!("<{}>", tag);
            let close_tag = format!("</{}>", tag);

            if let (Some(start_idx), Some(end_idx)) = (obfuscated.find(&open_tag), obfuscated.find(&close_tag)) {
                let content_start = start_idx + open_tag.len();
                if content_start < end_idx {
                    return Ok(obfuscated[content_start..end_idx].to_string());
                }
            }
        }

        // Try header format
        for header in ["Content-Type", "Authorization", "X-Token", "X-Data"] {
            let header_pattern = format!("{}:", header);
            if let Some(idx) = obfuscated.find(&header_pattern) {
                if let Some(data_start) = obfuscated[idx..].find("\r\n\r\n") {
                    let content_start = idx + data_start + 4; // Skip \r\n\r\n
                    if content_start < obfuscated.len() {
                        return Ok(obfuscated[content_start..].to_string());
                    }
                }
            }
        }

        Err(EncryptionError::Decryption("Could not extract data from mixed format".to_string()))
    }
}

/// Recursively collect files in a directory
fn collect_files(
    base_path: &Path,
    current_path: &Path,
    files: &mut Vec<(PathBuf, PathBuf)>,
) -> Result<()> {
    for entry in fs::read_dir(current_path)? {
        let entry = entry?;
        let path = entry.path();
        
        if path.is_dir() {
            collect_files(base_path, &path, files)?;
        } else {
            let relative_path = path.strip_prefix(base_path)
                .map_err(|_| EncryptionError::Io(io::Error::new(
                    io::ErrorKind::Other,
                    "Failed to get relative path",
                )))?
                .to_path_buf();
            files.push((relative_path, path));
        }
    }
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_text_encryption() {
        let qpe = QuantumProofEncryption::new(SecurityLevel::Standard);
        let (pub_key, priv_key) = qpe.generate_keypair().unwrap();
        
        let plaintext = "Hello, quantum world!";
        let encrypted = qpe.encrypt_text(plaintext, Some(&pub_key), None).unwrap();
        let decrypted = qpe.decrypt_text(&encrypted, Some(&priv_key), None).unwrap();
        
        assert_eq!(plaintext, decrypted);
    }

    #[test]
    fn test_password_encryption() {
        let qpe = QuantumProofEncryption::new(SecurityLevel::High);
        let password = "super-secret-password";
        
        let plaintext = "Test password encryption";
        let encrypted = qpe.encrypt_text(plaintext, None, Some(password)).unwrap();
        let decrypted = qpe.decrypt_text(&encrypted, None, Some(password)).unwrap();
        
        assert_eq!(plaintext, decrypted);
    }
}