use crate::errors::{EncryptionError, Result};
use crate::models::*;
use crate::utils::{decode_base64, encode_base64, generate_random_bytes, get_file_permissions};
use aes_gcm::{
    aead::{AeadInPlace, KeyInit},
    Aes256Gcm, Nonce, Tag,
};
use argon2::{Argon2, Params, Version};
use chrono::{DateTime, Utc};
use pqc_kyber::{decapsulate, encapsulate};
use serde_json;
use sha3::{Digest, Sha3_256};
use std::fs::{self, File};
use std::io::{BufReader, BufWriter, Read, Seek, Write};
use std::path::Path;
use filetime::{FileTime, set_file_mtime};

pub struct StreamingEncryptor<'a> {
    security_level: SecurityLevel,
    chunk_size: usize,
    progress_callback: Option<Box<dyn Fn(u64, u64) + 'a>>,
}

impl<'a> StreamingEncryptor<'a> {
    pub fn new(security_level: SecurityLevel) -> Self {
        Self {
            security_level,
            chunk_size: CHUNK_SIZE,
            progress_callback: None,
        }
    }

    pub fn with_progress_callback<F>(mut self, callback: F) -> Self
    where
        F: Fn(u64, u64) + 'a,
    {
        self.progress_callback = Some(Box::new(callback));
        self
    }

    /// Encrypt a stream using public key
    pub fn encrypt_stream_public_key<R: Read, W: Write>(
        &self,
        input: R,
        output: W,
        file_metadata: FileMetadata,
        public_key: &[u8],
    ) -> Result<()> {
        let iv = generate_random_bytes(IV_SIZE);
        
        // Generate KEM ciphertext and derive AES key
        let mut rng = rand::thread_rng();
        let (ciphertext, shared_secret) = encapsulate(&public_key, &mut rng)
            .map_err(|_| EncryptionError::Encryption("KEM encapsulation failed".to_string()))?;
        
        let mut hasher = Sha3_256::new();
        hasher.update(&shared_secret);
        let aes_key = hasher.finalize();
        
        let header = StreamHeader {
            format: "QPE-Stream".to_string(),
            version: FORMAT_VERSION.to_string(),
            security_level: self.security_level.as_str().to_string(),
            timestamp: Utc::now(),
            file_metadata,
            iv: encode_base64(&iv),
            algorithm: "Kyber-AES-GCM".to_string(),
            kyber_variant: Some(self.security_level.kyber_variant().name().to_string()),
            kem_ciphertext: Some(encode_base64(&ciphertext)),
            kdf_params: None,
        };
        
        self.encrypt_stream_internal(input, output, header, &aes_key, &iv)
    }

    /// Encrypt a stream using password
    pub fn encrypt_stream_password<R: Read, W: Write>(
        &self,
        input: R,
        output: W,
        file_metadata: FileMetadata,
        password: &str,
    ) -> Result<()> {
        let iv = generate_random_bytes(IV_SIZE);
        let salt = generate_random_bytes(SALT_SIZE);
        
        // Derive AES key using Argon2
        let params = self.security_level.argon2_params();
        let argon2_params = Params::new(
            params.memory_cost,
            params.time_cost,
            params.parallelism,
            Some(AES_KEY_SIZE),
        ).map_err(|e| EncryptionError::Argon2(e.to_string()))?;
        
        let argon2 = Argon2::new(argon2::Algorithm::Argon2id, Version::V0x13, argon2_params);
        
        let mut aes_key = vec![0u8; AES_KEY_SIZE];
        argon2.hash_password_into(password.as_bytes(), &salt, &mut aes_key)
            .map_err(|e| EncryptionError::Argon2(e.to_string()))?;
        
        let header = StreamHeader {
            format: "QPE-Stream".to_string(),
            version: FORMAT_VERSION.to_string(),
            security_level: self.security_level.as_str().to_string(),
            timestamp: Utc::now(),
            file_metadata,
            iv: encode_base64(&iv),
            algorithm: "Argon2id-AES-GCM".to_string(),
            kyber_variant: None,
            kem_ciphertext: None,
            kdf_params: Some(KdfParams {
                time_cost: params.time_cost,
                memory_cost: params.memory_cost,
                parallelism: params.parallelism,
                salt: encode_base64(&salt),
            }),
        };
        
        self.encrypt_stream_internal(input, output, header, &aes_key, &iv)
    }

    /// Internal encryption logic with true streaming for large files
    fn encrypt_stream_internal<R: Read, W: Write>(
        &self,
        mut input: R,
        mut output: W,
        header: StreamHeader,
        aes_key: &[u8],
        iv: &[u8],
    ) -> Result<()> {
        // Serialize header
        let header_bytes = serde_json::to_vec(&header)?;
        let header_len = header_bytes.len() as u64;

        // Write header length and header
        output.write_all(&header_len.to_be_bytes())?;
        output.write_all(&header_bytes)?;

        // Initialize cipher
        let cipher = Aes256Gcm::new_from_slice(aes_key)
            .map_err(|_| EncryptionError::Encryption("Invalid key length".to_string()))?;

        // For large files, we need to use ChaCha20Poly1305 or implement chunked GCM
        // For now, we'll implement a chunked approach with individual chunk encryption
        let mut buffer = vec![0u8; self.chunk_size];
        let mut total_read = 0u64;
        let total_size = header.file_metadata.original_size;
        let mut chunk_counter = 0u64;

        // Write total chunks count for verification
        let total_chunks = (total_size + self.chunk_size as u64 - 1) / self.chunk_size as u64;
        output.write_all(&total_chunks.to_be_bytes())?;

        loop {
            let bytes_read = input.read(&mut buffer)?;
            if bytes_read == 0 {
                break;
            }

            // Create unique nonce for each chunk by combining base IV with chunk counter
            let mut chunk_nonce = [0u8; 12];
            chunk_nonce[..8].copy_from_slice(&chunk_counter.to_be_bytes());
            chunk_nonce[8..].copy_from_slice(&iv[8..12]);
            let chunk_nonce = Nonce::from_slice(&chunk_nonce);

            // Encrypt this chunk
            let mut chunk_data = buffer[..bytes_read].to_vec();
            let tag = cipher.encrypt_in_place_detached(
                chunk_nonce,
                &header_bytes,
                &mut chunk_data
            ).map_err(|_| EncryptionError::Encryption("Chunk encryption failed".to_string()))?;

            // Write chunk size, encrypted data, and tag
            output.write_all(&(bytes_read as u32).to_be_bytes())?;
            output.write_all(&chunk_data)?;
            output.write_all(&tag)?;

            total_read += bytes_read as u64;
            chunk_counter += 1;

            // Call progress callback if available
            if let Some(ref callback) = self.progress_callback {
                callback(total_read, total_size);
            }
        }

        output.flush()?;
        Ok(())
    }

    /// Decrypt a stream using private key
    pub fn decrypt_stream_private_key<R: Read + Seek, W: Write>(
        &self,
        mut input: R,
        output: W,
        private_key: &[u8],
    ) -> Result<StreamHeader> {
        let (header, aes_key, iv) = self.read_header_and_derive_key(&mut input, Some(private_key), None)?;
        self.decrypt_stream_internal(input, output, &header, &aes_key, &iv)?;
        Ok(header)
    }

    /// Decrypt a stream using password
    pub fn decrypt_stream_password<R: Read + Seek, W: Write>(
        &self,
        mut input: R,
        output: W,
        password: &str,
    ) -> Result<StreamHeader> {
        let (header, aes_key, iv) = self.read_header_and_derive_key(&mut input, None, Some(password))?;
        self.decrypt_stream_internal(input, output, &header, &aes_key, &iv)?;
        Ok(header)
    }

    /// Read header and derive decryption key
    fn read_header_and_derive_key<R: Read + Seek>(
        &self,
        input: &mut R,
        private_key: Option<&[u8]>,
        password: Option<&str>,
    ) -> Result<(StreamHeader, Vec<u8>, Vec<u8>)> {
        // Read header length
        let mut header_len_bytes = [0u8; HEADER_LEN_BYTES];
        input.read_exact(&mut header_len_bytes)?;
        let header_len = u64::from_be_bytes(header_len_bytes);
        
        // Read header
        let mut header_bytes = vec![0u8; header_len as usize];
        input.read_exact(&mut header_bytes)?;
        let header: StreamHeader = serde_json::from_slice(&header_bytes)?;
        
        // Validate version
        if header.version != FORMAT_VERSION {
            return Err(EncryptionError::InvalidFormatVersion {
                expected: FORMAT_VERSION.to_string(),
                got: header.version,
            });
        }
        
        // Derive key based on algorithm
        let iv = decode_base64(&header.iv)?;
        let aes_key = match header.algorithm.as_str() {
            "Kyber-AES-GCM" => {
                let private_key = private_key
                    .ok_or_else(|| EncryptionError::Key("Private key required for Kyber-encrypted file".to_string()))?;
                let kem_ciphertext = header.kem_ciphertext.as_ref()
                    .ok_or_else(|| EncryptionError::CorruptedData("Missing KEM ciphertext".to_string()))?;
                let ciphertext_bytes = decode_base64(kem_ciphertext)?;
                
                let shared_secret = decapsulate(&ciphertext_bytes, private_key)
                    .map_err(|_| EncryptionError::Decryption("KEM decapsulation failed".to_string()))?;
                
                let mut hasher = Sha3_256::new();
                hasher.update(&shared_secret);
                hasher.finalize().to_vec()
            }
            "Argon2id-AES-GCM" => {
                let password = password
                    .ok_or_else(|| EncryptionError::Password("Password required for password-encrypted file".to_string()))?;
                let kdf_params = header.kdf_params.as_ref()
                    .ok_or_else(|| EncryptionError::CorruptedData("Missing KDF parameters".to_string()))?;
                let salt = decode_base64(&kdf_params.salt)?;
                
                let argon2_params = Params::new(
                    kdf_params.memory_cost,
                    kdf_params.time_cost,
                    kdf_params.parallelism,
                    Some(AES_KEY_SIZE),
                ).map_err(|e| EncryptionError::Argon2(e.to_string()))?;
                
                let argon2 = Argon2::new(argon2::Algorithm::Argon2id, Version::V0x13, argon2_params);
                
                let mut key = vec![0u8; AES_KEY_SIZE];
                argon2.hash_password_into(password.as_bytes(), &salt, &mut key)
                    .map_err(|e| EncryptionError::Argon2(e.to_string()))?;
                key
            }
            _ => return Err(EncryptionError::UnsupportedAlgorithm(header.algorithm.clone())),
        };
        
        Ok((header, aes_key, iv))
    }

    /// Internal decryption logic
    fn decrypt_stream_internal<R: Read + Seek, W: Write>(
        &self,
        mut input: R,
        mut output: W,
        header: &StreamHeader,
        aes_key: &[u8],
        iv: &[u8],
    ) -> Result<()> {
        // Initialize cipher
        let cipher = Aes256Gcm::new_from_slice(aes_key)
            .map_err(|_| EncryptionError::Decryption("Invalid key length".to_string()))?;

        // Serialize header for AAD
        let header_bytes = serde_json::to_vec(header)?;

        // Check if this is the new chunked format by looking at the version
        let is_chunked_format = header.version.starts_with("4.") || header.version.starts_with("5.");

        if is_chunked_format {
            // New chunked format
            // Read total chunks count
            let mut chunks_bytes = [0u8; 8];
            input.read_exact(&mut chunks_bytes)?;
            let total_chunks = u64::from_be_bytes(chunks_bytes);

            self.decrypt_chunked_format(input, output, header, &cipher, &header_bytes, iv, total_chunks)
        } else {
            // Legacy format - read entire file into memory
            self.decrypt_legacy_format(input, output, header, &cipher, &header_bytes, iv)
        }
    }

    /// Decrypt new chunked format for large files
    fn decrypt_chunked_format<R: Read, W: Write>(
        &self,
        mut input: R,
        mut output: W,
        header: &StreamHeader,
        cipher: &Aes256Gcm,
        header_bytes: &[u8],
        iv: &[u8],
        total_chunks: u64,
    ) -> Result<()> {
        let mut total_decrypted = 0u64;
        let total_size = header.file_metadata.original_size;

        // Process each chunk
        for chunk_counter in 0..total_chunks {
            // Read chunk size
            let mut size_bytes = [0u8; 4];
            input.read_exact(&mut size_bytes)?;
            let chunk_size = u32::from_be_bytes(size_bytes) as usize;

            // Read encrypted chunk data
            let mut chunk_data = vec![0u8; chunk_size];
            input.read_exact(&mut chunk_data)?;

            // Read tag
            let mut tag_bytes = [0u8; TAG_SIZE];
            input.read_exact(&mut tag_bytes)?;
            let tag = Tag::from_slice(&tag_bytes);

            // Create chunk-specific nonce (same logic as encryption)
            let mut chunk_nonce = [0u8; 12];
            chunk_nonce[..8].copy_from_slice(&chunk_counter.to_be_bytes());
            chunk_nonce[8..].copy_from_slice(&iv[8..12]);
            let chunk_nonce = Nonce::from_slice(&chunk_nonce);

            // Decrypt this chunk
            cipher.decrypt_in_place_detached(
                chunk_nonce,
                header_bytes,
                &mut chunk_data,
                tag
            ).map_err(|_| EncryptionError::Decryption("Chunk decryption failed".to_string()))?;

            // Write decrypted chunk
            output.write_all(&chunk_data)?;

            total_decrypted += chunk_data.len() as u64;

            // Call progress callback if available
            if let Some(ref callback) = self.progress_callback {
                callback(total_decrypted, total_size);
            }
        }

        output.flush()?;
        Ok(())
    }

    /// Decrypt legacy format (loads entire file into memory)
    fn decrypt_legacy_format<R: Read + Seek, W: Write>(
        &self,
        mut input: R,
        mut output: W,
        header: &StreamHeader,
        cipher: &Aes256Gcm,
        header_bytes: &[u8],
        iv: &[u8],
    ) -> Result<()> {
        use std::io::SeekFrom;

        // Get file size and calculate ciphertext boundaries
        let current_pos = input.stream_position()?;
        let file_size = input.seek(SeekFrom::End(0))?;
        let ciphertext_end = file_size - TAG_SIZE as u64;

        // Read tag
        input.seek(SeekFrom::Start(ciphertext_end))?;
        let mut tag_bytes = vec![0u8; TAG_SIZE];
        input.read_exact(&mut tag_bytes)?;
        let tag = Tag::from_slice(&tag_bytes);

        // Reset to start of ciphertext
        input.seek(SeekFrom::Start(current_pos))?;

        // Read all ciphertext for decryption
        let mut ciphertext = vec![0u8; (ciphertext_end - current_pos) as usize];
        input.read_exact(&mut ciphertext)?;

        let nonce = Nonce::from_slice(iv);

        // Decrypt and verify
        cipher.decrypt_in_place_detached(
            nonce,
            header_bytes,
            &mut ciphertext,
            tag
        ).map_err(|_| EncryptionError::Decryption("Decryption failed: Invalid tag or corrupted data".to_string()))?;

        // Write decrypted data
        output.write_all(&ciphertext)?;

        if let Some(ref callback) = self.progress_callback {
            callback(ciphertext.len() as u64, header.file_metadata.original_size);
        }

        output.flush()?;
        Ok(())
    }
}

/// High-level function to encrypt a file
pub fn encrypt_file(
    input_path: &Path,
    output_path: &Path,
    security_level: SecurityLevel,
    public_key: Option<&[u8]>,
    password: Option<&str>,
) -> Result<()> {
    if public_key.is_none() && password.is_none() {
        return Err(EncryptionError::Key("Either public key or password must be provided".to_string()));
    }
    if public_key.is_some() && password.is_some() {
        return Err(EncryptionError::Key("Provide either public key or password, not both".to_string()));
    }
    
    let metadata = fs::metadata(input_path)?;
    let file_metadata = FileMetadata {
        original_name: input_path.file_name()
            .ok_or_else(|| EncryptionError::FileNotFound("Invalid file name".to_string()))?
            .to_string_lossy()
            .to_string(),
        original_size: metadata.len(),
        modified: DateTime::<Utc>::from(metadata.modified()?),
        permissions: get_file_permissions(input_path)?,
    };
    
    let input_file = File::open(input_path)?;
    let output_file = File::create(output_path)?;
    let mut reader = BufReader::new(input_file);
    let mut writer = BufWriter::new(output_file);
    
    let encryptor = StreamingEncryptor::new(security_level);
    
    if let Some(pub_key) = public_key {
        encryptor.encrypt_stream_public_key(&mut reader, &mut writer, file_metadata, pub_key)?;
    } else if let Some(pwd) = password {
        encryptor.encrypt_stream_password(&mut reader, &mut writer, file_metadata, pwd)?;
    }
    
    Ok(())
}

/// High-level function to decrypt a file
pub fn decrypt_file(
    input_path: &Path,
    output_path: &Path,
    security_level: SecurityLevel,
    private_key: Option<&[u8]>,
    password: Option<&str>,
) -> Result<()> {
    let input_file = File::open(input_path)?;
    let output_file = File::create(output_path)?;
    let mut reader = BufReader::new(input_file);
    let mut writer = BufWriter::new(output_file);
    
    let decryptor = StreamingEncryptor::new(security_level);
    
    let header = if let Some(priv_key) = private_key {
        decryptor.decrypt_stream_private_key(&mut reader, &mut writer, priv_key)?
    } else if let Some(pwd) = password {
        decryptor.decrypt_stream_password(&mut reader, &mut writer, pwd)?
    } else {
        return Err(EncryptionError::Key("Either private key or password must be provided".to_string()));
    };
    
    // Restore file metadata
    drop(writer); // Ensure file is closed before setting metadata
    
    let mtime: i64 = header.file_metadata.modified.timestamp();
    let file_time = FileTime::from_unix_time(mtime, 0);
    let _ = set_file_mtime(output_path, file_time);
    
    #[cfg(unix)]
    if let Some(perms) = header.file_metadata.permissions {
        if let Ok(mode) = u32::from_str_radix(&perms, 8) {
            crate::utils::set_file_permissions(output_path, mode)?;
        }
    }
    
    Ok(())
}