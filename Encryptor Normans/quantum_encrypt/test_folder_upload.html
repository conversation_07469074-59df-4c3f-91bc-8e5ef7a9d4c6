<!DOCTYPE html>
<html>
<head>
    <title>Folder Upload Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border-radius: 8px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; }
        h1 { color: #2c3e50; text-align: center; }
        h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
        input[type="file"] { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; width: 100%; }
        select { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📁 Folder Upload & Encryption Test</h1>
        
        <div class="test-section info">
            <h2>Test Folder Encryption</h2>
            <p>Select multiple files to test folder encryption and download functionality:</p>
            
            <input type="file" id="test-files" multiple accept="*/*">
            
            <div>
                <label>Method:</label>
                <select id="test-method">
                    <option value="password">Password</option>
                    <option value="public-key">Public Key</option>
                </select>
            </div>
            
            <div id="password-section">
                <label>Password:</label>
                <input type="password" id="test-password" value="testpass123">
            </div>
            
            <div id="pubkey-section" style="display: none;">
                <label>Public Key File:</label>
                <input type="file" id="test-pubkey" accept=".key">
            </div>
            
            <button class="test-button" onclick="testFolderEncryption()">Test Folder Encryption</button>
            <button class="test-button" onclick="testLargeFolderUpload()">Test Large Folder API</button>
            
            <div id="test-results"></div>
        </div>

        <div class="test-section success">
            <h2>✅ API Test Results</h2>
            <p>Both password and public key folder encryption are working correctly at the API level:</p>
            <ul>
                <li><strong>✅ Password Method:</strong> tar.gz → encrypted .qpe → downloadable</li>
                <li><strong>✅ Public Key Method:</strong> tar.gz → encrypted .qpe → downloadable</li>
                <li><strong>✅ Download Streaming:</strong> Large files use streaming downloads</li>
                <li><strong>✅ Progress Tracking:</strong> Real-time progress with result_data</li>
            </ul>
        </div>
    </div>

    <script>
        // Show/hide sections based on method
        document.getElementById('test-method').addEventListener('change', function() {
            const method = this.value;
            document.getElementById('password-section').style.display = method === 'password' ? 'block' : 'none';
            document.getElementById('pubkey-section').style.display = method === 'public-key' ? 'block' : 'none';
        });

        // Test folder encryption using the regular upload method
        async function testFolderEncryption() {
            const files = document.getElementById('test-files').files;
            const method = document.getElementById('test-method').value;
            const resultsDiv = document.getElementById('test-results');
            
            if (files.length === 0) {
                resultsDiv.innerHTML = '<div class="result error">Please select files first!</div>';
                return;
            }

            resultsDiv.innerHTML = '<div class="result info">Testing folder encryption...</div>';

            try {
                const formData = new FormData();
                formData.append('operation_type', 'folder');
                formData.append('method', method);
                formData.append('security_level', 'high');
                formData.append('compression_level', 'high');

                if (method === 'password') {
                    const password = document.getElementById('test-password').value;
                    formData.append('password', password);
                } else {
                    const pubkeyFile = document.getElementById('test-pubkey').files[0];
                    if (!pubkeyFile) {
                        resultsDiv.innerHTML = '<div class="result error">Please select a public key file!</div>';
                        return;
                    }
                    formData.append('public_key', pubkeyFile);
                }

                // Add all files
                for (let i = 0; i < files.length; i++) {
                    formData.append('file', files[i]);
                }

                const response = await fetch('/api/encrypt-upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    resultsDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Folder Encryption Success!</h3>
                            <p><strong>Files:</strong> ${files.length}</p>
                            <p><strong>Method:</strong> ${method}</p>
                            <p><strong>Download ID:</strong> ${result.data.download_id}</p>
                            <p><strong>Output:</strong> ${result.data.output_filename}</p>
                            <p><strong>Size:</strong> ${result.data.original_size} → ${result.data.encrypted_size} bytes</p>
                            <p><strong>Compression:</strong> ${result.data.compression_ratio}</p>
                            <button class="test-button" onclick="testDownload('${result.data.download_url}', '${result.data.output_filename}')">Download Encrypted Folder</button>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Folder Encryption Failed</h3>
                            <p>${result.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Test large folder upload API directly
        async function testLargeFolderUpload() {
            const files = document.getElementById('test-files').files;
            const method = document.getElementById('test-method').value;
            const resultsDiv = document.getElementById('test-results');
            
            if (files.length === 0) {
                resultsDiv.innerHTML = '<div class="result error">Please select files first!</div>';
                return;
            }

            resultsDiv.innerHTML = '<div class="result info">Testing large folder upload API...</div>';

            try {
                const formData = new FormData();
                formData.append('method', method);
                formData.append('security_level', 'high');
                formData.append('compression_level', 'high');

                if (method === 'password') {
                    const password = document.getElementById('test-password').value;
                    formData.append('password', password);
                } else {
                    const pubkeyFile = document.getElementById('test-pubkey').files[0];
                    if (!pubkeyFile) {
                        resultsDiv.innerHTML = '<div class="result error">Please select a public key file!</div>';
                        return;
                    }
                    formData.append('public_key', pubkeyFile);
                }

                // Add all files
                for (let i = 0; i < files.length; i++) {
                    formData.append('file', files[i]);
                }

                const response = await fetch('/api/large-folder-upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    resultsDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Large Folder Upload Success!</h3>
                            <p><strong>Files:</strong> ${result.data.original_files}</p>
                            <p><strong>Method:</strong> ${method}</p>
                            <p><strong>Download ID:</strong> ${result.data.download_id}</p>
                            <p><strong>Output:</strong> ${result.data.output_filename}</p>
                            <p><strong>Size:</strong> ${result.data.original_size} → ${result.data.encrypted_size} bytes</p>
                            <p><strong>Compression:</strong> ${result.data.compression_ratio}</p>
                            <p><strong>Upload Method:</strong> ${result.data.upload_method}</p>
                            <button class="test-button" onclick="testDownload('${result.data.download_url}', '${result.data.output_filename}')">Download Encrypted Folder</button>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Large Folder Upload Failed</h3>
                            <p>${result.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Test download functionality
        function testDownload(downloadUrl, filename) {
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = filename;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML += `
                <div class="result success">
                    <h3>✅ Download Initiated</h3>
                    <p>Download started for: ${filename}</p>
                    <p>URL: ${downloadUrl}</p>
                </div>
            `;
        }
    </script>
</body>
</html>
