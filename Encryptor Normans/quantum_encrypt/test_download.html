<!DOCTYPE html>
<html>
<head>
    <title>Download Test</title>
</head>
<body>
    <h1>Download Test</h1>
    <button onclick="testDownload()">Test Download Function</button>
    <div id="result"></div>

    <script>
        // Copy the updated download function from script.js
        async function downloadFile(downloadId, filename) {
            const button = event.target;
            if (button.disabled) {
                return;
            }

            try {
                button.disabled = true;
                const originalText = button.innerHTML;
                button.innerHTML = 'Downloading...';

                // For large files, use direct link approach to avoid memory issues
                // Create direct download link - this avoids loading large files into memory
                const a = document.createElement('a');
                a.href = `/api/download/${downloadId}`;
                a.download = filename;
                a.style.display = 'none';
                document.body.appendChild(a);
                a.click();

                // Clean up
                document.body.removeChild(a);

                // Show success after a short delay
                setTimeout(() => {
                    document.getElementById('result').innerHTML = '<p style="color: green;">Download started successfully!</p>';
                }, 500);

                // Update button to show success
                button.innerHTML = 'Download Started';
                button.style.background = '#28a745';

                // Re-enable button after a delay
                setTimeout(() => {
                    button.disabled = false;
                    button.innerHTML = originalText;
                    button.style.background = '';
                }, 3000);

            } catch (error) {
                document.getElementById('result').innerHTML = `<p style="color: red;">Download failed: ${error.message}</p>`;

                // Re-enable button on error
                button.disabled = false;
                button.innerHTML = originalText;
                button.style.background = '';
            }
        }

        function testDownload() {
            // Use the download ID from our latest test
            downloadFile('download_1751488121409', 'medium_test.bin');
        }
    </script>
</body>
</html>
