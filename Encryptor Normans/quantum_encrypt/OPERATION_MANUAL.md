# Norman's Quantum-Proof Encryption Program
## Operation Manual

---

## 📋 **Table of Contents**

1. [System Overview](#system-overview)
2. [Installation & Setup](#installation--setup)
3. [Web Interface Operations](#web-interface-operations)
4. [Command Line Operations](#command-line-operations)
5. [Security Best Practices](#security-best-practices)
6. [Troubleshooting](#troubleshooting)
7. [Maintenance](#maintenance)

---

## 🔍 **System Overview**

### **Architecture**
- **Frontend**: Modern HTML5 web interface with responsive design
- **Backend**: Rust-based server with streaming file processing
- **Encryption**: CRYSTALS-Kyber + AES-256-GCM with Ghost-Mode obfuscation
- **Storage**: Temporary file processing with automatic cleanup

### **Key Components**
- **Web Server**: HTTP server for browser interface
- **Encryption Engine**: Post-quantum cryptography implementation
- **File Processor**: Streaming handler for large files (up to 25GB)
- **Progress Tracker**: Real-time operation monitoring

---

## 🚀 **Installation & Setup**

### **Prerequisites**
```bash
# Install Rust (if not already installed)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Verify installation
rustc --version
cargo --version
```

### **Build Application**
```bash
# Clone repository
git clone <repository-url>
cd quantum_encrypt

# Build release version
cargo build --release --features web-server

# Verify build
./target/release/normans-quantum-proof-encryption --version
```

### **Start Web Server**
```bash
# Start on default port (8080)
./target/release/normans-quantum-proof-encryption --web

# Start on custom port
./target/release/normans-quantum-proof-encryption --web --port 9000

# Start with verbose logging
RUST_LOG=info ./target/release/normans-quantum-proof-encryption --web --port 8080
```

---

## 🌐 **Web Interface Operations**

### **Accessing the Interface**
1. Start the web server
2. Open browser and navigate to `http://localhost:8080`
3. You'll see the main interface with Encrypt/Decrypt tabs

### **Text Encryption**
1. **Select "Encrypt" tab**
2. **Choose "Text" operation**
3. **Enter your text** in the input field
4. **Select encryption method**:
   - **Password**: Enter a secure password
   - **Public Key**: Upload a .pub key file
5. **Click "Encrypt"**
6. **Wait for "Please wait..." banner** to complete
7. **Copy encrypted text** using the "Copy to Clipboard" button

### **Text Decryption**
1. **Select "Decrypt" tab**
2. **Choose "Text" operation**
3. **Paste encrypted text** (Ghost-Mode format: `FORMAT:ALGORITHM:NOISE:DATA:TRAILER`)
4. **Select decryption method**:
   - **Password**: Enter the same password used for encryption
   - **Private Key**: Upload the corresponding .key file
5. **Click "Decrypt"**
6. **Copy decrypted text** using the "Copy to Clipboard" button

### **File Encryption**
1. **Select "Encrypt" tab**
2. **Choose "File" operation**
3. **Upload file** (drag & drop or click "Choose File")
4. **Select security level**: Standard, High, or Maximum
5. **Choose encryption method** and provide credentials
6. **Click "Encrypt"**
7. **Monitor progress** with "Please wait..." banner
8. **Download encrypted file** when complete

### **Folder Encryption**
1. **Select "Encrypt" tab**
2. **Choose "Folder" operation**
3. **Upload multiple files** or a ZIP archive
4. **Select compression level**: Low, High, or Maximum
5. **Choose encryption method** and provide credentials
6. **Click "Encrypt"**
7. **Download encrypted archive** when complete

---

## 💻 **Command Line Operations**

### **Generate Key Pair**
```bash
./target/release/normans-quantum-proof-encryption generate-keys \
  --public-key alice.pub \
  --private-key alice.key \
  --password "secure-password"
```

### **Encrypt File with Password**
```bash
./target/release/normans-quantum-proof-encryption encrypt \
  --input document.pdf \
  --output document.pdf.qpe \
  --password "your-password" \
  --security-level high
```

### **Encrypt File with Public Key**
```bash
./target/release/normans-quantum-proof-encryption encrypt \
  --input document.pdf \
  --output document.pdf.qpe \
  --public-key alice.pub \
  --security-level maximum
```

### **Decrypt File**
```bash
./target/release/normans-quantum-proof-encryption decrypt \
  --input document.pdf.qpe \
  --output document.pdf \
  --private-key alice.key \
  --password "key-password"
```

### **Encrypt Folder**
```bash
./target/release/normans-quantum-proof-encryption encrypt-folder \
  --input /path/to/folder \
  --output encrypted_folder.qpe \
  --password "folder-password" \
  --compression-level 9
```

---

## 🔐 **Security Best Practices**

### **Password Security**
- **Minimum Length**: 12 characters
- **Complexity**: Mix of uppercase, lowercase, numbers, and symbols
- **Uniqueness**: Use different passwords for different files
- **Storage**: Use a password manager, never store in plain text

### **Key Management**
- **Private Key Protection**: Always encrypt private keys with strong passwords
- **Key Storage**: Store private keys in secure, offline locations
- **Key Backup**: Maintain secure backups of all key pairs
- **Key Rotation**: Generate new keys periodically for high-security applications

### **File Handling**
- **Secure Deletion**: Original files are automatically wiped after encryption
- **Temporary Files**: Application automatically cleans up temporary files
- **Download Security**: Downloaded files are automatically deleted from server
- **Network Security**: Use HTTPS in production environments

### **Ghost-Mode Benefits**
- **AI Resistance**: Encrypted text cannot be analyzed by AI systems
- **Metadata Protection**: File sizes and types are completely hidden
- **Pattern Obfuscation**: No recognizable encryption signatures
- **Format Disguise**: Appears as random data streams

---

## 🔧 **Configuration Options**

### **Environment Variables**
```bash
# Logging level
export RUST_LOG=info

# Custom port
export PORT=9000

# Maximum file size (bytes)
export MAX_FILE_SIZE=26843545600  # 25GB

# Temporary directory
export TEMP_DIR=/tmp/quantum_encrypt
```

### **Security Levels**
- **Standard**: Kyber512 + AES-256-GCM (fast, NIST Level 1)
- **High**: Kyber768 + AES-256-GCM (balanced, NIST Level 3)
- **Maximum**: Kyber1024 + AES-256-GCM (strongest, NIST Level 5)

### **Compression Levels**
- **Low (1-3)**: Fast compression, ~40-50% size reduction
- **High (4-6)**: Balanced compression, ~60-70% size reduction
- **Maximum (7-9)**: Best compression, ~70-85% size reduction

---

## 📊 **Performance Guidelines**

### **File Size Recommendations**
- **Small files (<100MB)**: Stored in memory for fast processing
- **Medium files (100MB-1GB)**: Streamed processing with progress tracking
- **Large files (1GB-25GB)**: Chunked streaming with memory optimization

### **System Resources**
- **RAM Usage**: <100MB regardless of file size
- **CPU Usage**: Scales with security level and compression
- **Disk Space**: Temporary space = 2x largest file size
- **Network**: Bandwidth depends on file size and connection speed

### **Optimization Tips**
- **Use appropriate security level** for your threat model
- **Choose compression level** based on file types
- **Monitor system resources** during large file operations
- **Use SSD storage** for better performance with large files

---

## 🎯 **Use Cases**

### **Personal Use**
- **Document Protection**: Secure personal documents and photos
- **Communication**: Encrypted messaging with Ghost-Mode obfuscation
- **Backup Security**: Encrypt backups before cloud storage

### **Business Use**
- **Confidential Documents**: Protect sensitive business information
- **Client Data**: Secure customer information and communications
- **Intellectual Property**: Protect trade secrets and proprietary data

### **Enterprise Use**
- **Compliance**: Meet regulatory requirements (GDPR, HIPAA, SOX)
- **Data Protection**: Secure large databases and file systems
- **Communication Security**: Encrypted inter-office communications

---

## 📈 **Monitoring & Logging**

### **Enable Logging**
```bash
# Info level logging
RUST_LOG=info ./target/release/normans-quantum-proof-encryption --web

# Debug level logging
RUST_LOG=debug ./target/release/normans-quantum-proof-encryption --web

# Specific module logging
RUST_LOG=quantum_encrypt::encryption=debug ./target/release/normans-quantum-proof-encryption --web
```

### **Log Analysis**
- **Operation Tracking**: All encrypt/decrypt operations are logged
- **Performance Metrics**: File sizes, processing times, and memory usage
- **Error Tracking**: Detailed error messages for troubleshooting
- **Security Events**: Failed authentication attempts and invalid operations

---

This operation manual provides comprehensive guidance for using Norman's Quantum-Proof Encryption Program effectively and securely.
