<!DOCTYPE html>
<html>
<head>
    <title>Frontend Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <h1>Frontend Fix Test</h1>
    <button onclick="testProgressAPI()">Test Progress API</button>
    <button onclick="testShowDecryptionResult()">Test showDecryptionResult Function</button>
    <div id="results"></div>

    <script>
        // Copy the formatBytes function from script.js
        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Test the progress API response
        async function testProgressAPI() {
            try {
                const response = await fetch('/api/progress/decrypt_1751489052076');
                const result = await response.json();
                
                const resultsDiv = document.getElementById('results');
                
                if (result.success && result.data && result.data.result_data) {
                    const resultData = result.data.result_data;
                    resultsDiv.innerHTML += `
                        <div class="test-result success">
                            <h3>✅ Progress API Test - SUCCESS</h3>
                            <p><strong>Download ID:</strong> ${resultData.download_id}</p>
                            <p><strong>Output Filename:</strong> ${resultData.output_filename}</p>
                            <p><strong>Decrypted Size:</strong> ${formatBytes(resultData.decrypted_size)}</p>
                            <p><strong>Encrypted Size:</strong> ${formatBytes(resultData.encrypted_size)}</p>
                            <p><strong>Type:</strong> ${resultData.type}</p>
                            <p><strong>Message:</strong> ${resultData.message}</p>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML += `
                        <div class="test-result error">
                            <h3>❌ Progress API Test - FAILED</h3>
                            <p>No result_data found in response</p>
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('results').innerHTML += `
                    <div class="test-result error">
                        <h3>❌ Progress API Test - ERROR</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Test the showDecryptionResult function with sample data
        function testShowDecryptionResult() {
            const sampleResultData = {
                download_id: "download_1751489053251",
                download_url: "/api/download/download_1751489053251",
                output_filename: "final_large_test.bin_decrypted",
                decrypted_size: 209715200,
                encrypted_size: 209715654,
                type: "file",
                message: "Large file decrypted successfully!"
            };

            // Create a test display
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `
                <div class="test-result success">
                    <h3>✅ showDecryptionResult Test - Sample Data</h3>
                    <p><strong>Message:</strong> ${sampleResultData.message}</p>
                    <p><strong>Encrypted size:</strong> ${formatBytes(sampleResultData.encrypted_size)}</p>
                    <p><strong>Decrypted size:</strong> ${formatBytes(sampleResultData.decrypted_size)}</p>
                    <p><strong>Output file:</strong> ${sampleResultData.output_filename}</p>
                    <p><strong>Type:</strong> ${sampleResultData.type === 'folder' ? 'Folder Archive (ZIP)' : 'Single File'}</p>
                    <p><strong>Download ID:</strong> ${sampleResultData.download_id}</p>
                    <button onclick="window.open('${sampleResultData.download_url}', '_blank')">Test Download Link</button>
                </div>
            `;
        }
    </script>
</body>
</html>
